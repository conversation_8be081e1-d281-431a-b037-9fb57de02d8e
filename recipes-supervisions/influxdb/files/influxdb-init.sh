#!/bin/bash

# InfluxDB initialization script for i2R
# This script sets up the initial organization, bucket, and user

INFLUX_HOST="http://localhost:8086"
ORG_NAME="i2r"
BUCKET_NAME="telegraf"
USERNAME="i2r-admin"
PASSWORD="i2r-password"
RETENTION="30d"

# Wait for InfluxDB to be ready
echo "Waiting for InfluxDB to be ready..."
for i in {1..30}; do
    if curl -s "${INFLUX_HOST}/health" > /dev/null 2>&1; then
        echo "InfluxDB is ready!"
        break
    fi
    echo "Waiting for InfluxDB... (attempt $i/30)"
    sleep 2
done

# Check if InfluxDB is already initialized
if curl -s "${INFLUX_HOST}/api/v2/setup" | grep -q '"allowed":false'; then
    echo "InfluxDB is already initialized"
    exit 0
fi

# Initialize InfluxDB
echo "Initializing InfluxDB..."
SETUP_RESPONSE=$(curl -s -X POST "${INFLUX_HOST}/api/v2/setup" \
    -H "Content-Type: application/json" \
    -d "{
        \"username\": \"${USERNAME}\",
        \"password\": \"${PASSWORD}\",
        \"org\": \"${ORG_NAME}\",
        \"bucket\": \"${BUCKET_NAME}\",
        \"retentionPeriodSeconds\": 2592000
    }")

if echo "$SETUP_RESPONSE" | grep -q '"id"'; then
    echo "InfluxDB initialized successfully!"
    
    # Extract the token from the response
    TOKEN=$(echo "$SETUP_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$TOKEN" ]; then
        echo "Generated token: $TOKEN"
        echo "Organization: $ORG_NAME"
        echo "Bucket: $BUCKET_NAME"
        echo "Username: $USERNAME"
        echo ""
        echo "You can use this token to configure Telegraf and other clients."
        echo "Update the Telegraf configuration with this token."
    fi
else
    echo "Failed to initialize InfluxDB"
    echo "Response: $SETUP_RESPONSE"
    exit 1
fi
